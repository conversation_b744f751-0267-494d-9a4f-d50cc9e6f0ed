import { createContext, useState } from "react";

type AuthUserType = {
  name: string;
  email: string;
};

export const UserContext = createContext<AuthUserType | null>(null);

export const UserContextProvider = (children: React.ReactNode) => {
  // create useState to save the user data
  const [user, setUser] = useState<AuthUserType | null>(null);

  return <UserContext.Provider value={null}>{children}</UserContext.Provider>;
};
