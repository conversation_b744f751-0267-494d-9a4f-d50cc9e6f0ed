import { createContext, useState } from "react";

type AuthUserType = {
  name: string;
  email: string;
};

type UserContextProviderProps = {
  children: React.ReactNode;
};

type UserContextType = {
  user: AuthUserType | null;
  setUser: React.Dispatch<React.SetStateAction<AuthUserType | null>>;
};

// export const UserContext = createContext<userContextType | null>(null);
// or
// eslint-disable-next-line react-refresh/only-export-components
export const UserContext = createContext<UserContextType>(
  {} as UserContextType
);

export const UserContextProvider = ({ children }: UserContextProviderProps) => {
  // create useState to save the user data
  const [user, setUser] = useState<AuthUserType | null>(null);

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};
