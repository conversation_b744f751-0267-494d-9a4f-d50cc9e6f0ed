// import { createContext } from "react";
// import { themes } from "./theme";

// type ThemeContextProvider = {
//   children: React.ReactNode;
// };

// export const ThemeContext = createContext(themes);

// export const ThemeContextProvider = ({ children }: ThemeContextProvider) => {
//   return (
//     <ThemeContext.Provider value={themes}>{children}</ThemeContext.Provider>
//   );
// };

// ThemeContext.tsx
import { createContext, useEffect, useState } from "react";
import { themes } from "./theme";

type Theme = "light" | "dark";

type ThemeContextType = {
  theme: Theme;
  themeData: (typeof themes)["light"];
  toggleTheme: () => void;
};

export const ThemeContext = createContext<ThemeContextType>({
  theme: "light",
  themeData: themes.light,
  toggleTheme: () => {},
});

type ThemeContextProviderProps = {
  children: React.ReactNode;
};

export const ThemeContextProvider = ({
  children,
}: ThemeContextProviderProps) => {
  // Read from localStorage (or fallback to 'light')
  const [theme, setTheme] = useState<Theme>(() => {
    const stored = localStorage.getItem("theme");
    return stored === "dark" ? "dark" : "light";
  });

  const toggleTheme = () => {
    setTheme((prev) => (prev === "light" ? "dark" : "light"));
  };

  // Set body background and text, and persist theme
  useEffect(() => {
    const themeColors = themes[theme];
    document.body.style.backgroundColor = themeColors.main;
    document.body.style.color = themeColors.text;

    // Save to localStorage
    localStorage.setItem("theme", theme);
  }, [theme]);

  const value: ThemeContextType = {
    theme,
    themeData: themes[theme],
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};
