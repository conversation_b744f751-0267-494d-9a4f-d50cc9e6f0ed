import { useContext } from "react";
import { UserContext } from "../context/userContext";

function User() {
  // call user context
  const userContext = useContext(UserContext);

  const hadlesLogin = () => {
    if (userContext) {
      userContext.setUser({
        name: "<PERSON><PERSON>",
        email: "<EMAIL>",
      });
    }
  };
  const handleLogout = () => {
    if (userContext) {
      userContext.setUser(null);
    }
  };

  return (
    <div>
      <button onClick={hadlesLogin}>Login</button>
      <button onClick={handleLogout}>Logout</button>
      <div> User name is: {userContext?.user?.name} </div>
      <div> User email is: {userContext?.user?.email} </div>
    </div>
  );
}

export default User;