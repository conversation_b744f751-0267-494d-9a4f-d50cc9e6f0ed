type PropsType = {
  children: React.ReactNode;
};

function Layout({ children }: PropsType) {
  return (
    <div>
      <header>
        <h1>This is the header</h1>
      </header>
      <main>
        <b>This is the Children:</b>
        <div style={{ border: "1px solid black", padding: "10px" }}>
          {children}
        </div>
      </main>
      <footer>
        <p> This is the footer</p>
      </footer>
    </div>
  );
}

export default Layout;
