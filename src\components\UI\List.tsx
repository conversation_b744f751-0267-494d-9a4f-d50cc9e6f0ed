import * as React from "react";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select, { SelectChangeEvent } from "@mui/material/Select";

export default function BasicSelect() {
  const [person, setAge] = React.useState("");

  const persons = [
    { name: "<PERSON><PERSON>", age: 20 },
    { name: "<PERSON>", age: 21 },
    { name: "<PERSON>", age: 22 },
  ];

  const handleChange = (event: SelectChangeEvent) => {
    setAge(event.target.value as string);
  };

  return (
    <FormControl fullWidth>
      <InputLabel id="demo-simple-select-label">Persons</InputLabel>
      <Select
        labelId="demo-simple-select-label"
        id="demo-simple-select"
        value={person}
        label="Persons"
        onChange={handleChange}
      >
        {persons.map((item) => (
          <MenuItem key={item.name} value={item.name}>
            {item.name}
          </MenuItem>
        ))}
      </Select>

      <p>This is the selected person: {person}</p>
    </FormControl>
  );
}
