import { createBrowser<PERSON>outer } from "react-router-dom";
import DefaultLayout from "./Layouts/default";
import AdminLayout from "./Layouts/admin";
import ClientLayout from "./Layouts/client";

import Home from "./pages/Home";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Dashboard from "./pages/Dashboard";

import InboxIcon from "@mui/icons-material/MoveToInbox";
import MailIcon from "@mui/icons-material/Mail";
import DashboardIcon from "@mui/icons-material/Dashboard";

import type { ReactNode } from "react";

export interface AppRoute {
  path: string;
  name: string;
  element: ReactNode;
  icon: ReactNode;
  layout: string;
}

export const appRoutes: AppRoute[] = [
  {
    path: "/",
    name: "Home",
    element: <Home />,
    icon: <InboxIcon />,
    layout: "Default",
  },
  {
    path: "/about",
    name: "About",
    element: <About />,
    icon: <MailIcon />,
    layout: "Default",
  },
  {
    path: "/contact",
    name: "Contact",
    element: <Contact />,
    icon: <MailIcon />,
    layout: "Client",
  },
  {
    path: "/dashboard",
    name: "Dashboard",
    element: <Dashboard />,
    icon: <DashboardIcon />,
    layout: "Admin",
  },
];

const layoutMap: Record<string, React.ComponentType<{ routes: AppRoute[] }>> = {
  Default: DefaultLayout,
  Admin: AdminLayout,
  Client: ClientLayout,
};

const groupedRoutes: Record<string, AppRoute[]> = {};

appRoutes.forEach((route) => {
  if (!groupedRoutes[route.layout]) {
    groupedRoutes[route.layout] = [];
  }
  groupedRoutes[route.layout].push(route);
});

export const router = createBrowserRouter(
  Object.entries(groupedRoutes).map(([layoutName, routes]) => {
    const LayoutComponent = layoutMap[layoutName];
    return {
      path: "/",
      element: <LayoutComponent routes={routes} />,
      children: routes.map((route) => ({
        path: route.path === "/" ? "" : route.path.replace("/", ""),
        element: route.element,
      })),
    };
  })
);
