import React, { useState, useMemo } from "react";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { CssBaseline, IconButton, Box } from "@mui/material";
import Brightness4Icon from "@mui/icons-material/Brightness4"; // moon
import Brightness7Icon from "@mui/icons-material/Brightness7"; // sun

function Theme() {
  const [mode, setMode] = useState("light");

  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode, // either 'light' or 'dark'
        },
      }),
    [mode]
  );

  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === "light" ? "dark" : "light"));
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline /> {/* Apply base styles for light/dark */}
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
        bgcolor="background.default"
        color="text.primary"
      >
        <IconButton onClick={toggleTheme} color="inherit">
          {mode === "light" ? <Brightness4Icon /> : <Brightness7Icon />}
        </IconButton>
      </Box>
    </ThemeProvider>
  );
}

export default Theme;
