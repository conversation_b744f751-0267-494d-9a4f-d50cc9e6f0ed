// import { useContext, useState } from "react";
// import reactLogo from "./assets/react.svg";
// import viteLogo from "/vite.svg";
// import "./App.css";
// import Welcome from "./components/Welcome";
// import Peoples from "./components/Peoples";
// import Layout from "./components/Layout";
// import { ThemeContext } from "./context/ThemeContext";
// import { UserContextProvider } from "./context/userContext";
// import User from "./components/user";
// import CustomButton from "./components/CustomButton";
// import Button from "@mui/material/Button";
// import Theme from "./components/Theme";
// import { Rating } from "@mui/material";
// import List from "./components/UI/List";
// import Drawer from "./components/UI/Drawer";

// function App() {
//   const [count, setCount] = useState(0);
//   const people = [
//     { name: "<PERSON><PERSON>", age: 20 },
//     { name: "<PERSON>", age: 21 },
//     { name: "<PERSON>", age: 22 },
//   ];

//   const { theme, toggleTheme } = useContext(ThemeContext);

//   return (
//     <>
//       <button onClick={toggleTheme}>
//         Switch to {theme === "light" ? "Dark" : "Light"} Mode
//       </button>

//       <div>
//         <a href="https://vite.dev" target="_blank">
//           <img src={viteLogo} className="logo" alt="Vite logo" />
//         </a>
//         <a href="https://react.dev" target="_blank">
//           <img src={reactLogo} className="logo react" alt="React logo" />
//         </a>
//       </div>
//       <h1>Vite + React</h1>
//       <div className="card">
//         <button onClick={() => setCount((count) => count + 1)}>
//           count is {count}
//         </button>
//       </div>

//       <div>
//         <Welcome name="Anas" copyright={2023} />
//       </div>

//       <div>
//         <Peoples peopleData={people} />
//       </div>

//       <div>
//         <Layout>
//           <Welcome name="Anas" copyright={2023} />
//         </Layout>
//       </div>

//       <div>
//         <UserContextProvider>
//           <User />
//         </UserContextProvider>
//       </div>

//       <div>
//         <CustomButton
//           variant="primary"
//           onClick={() => {
//             console.log("Primary Button Clicked");
//           }}
//         >
//           Primary Button
//         </CustomButton>
//       </div>

//       <div>
//         <Button variant="contained" color="primary">
//           Material UI
//         </Button>
//       </div>

//       {/* <div>
//         <Theme />
//       </div> */}

//       <Rating />
//       <List />
//       <Drawer />
//     </>
//   );
// }

// export default App;

import { RouterProvider, } from "react-router-dom";
import { router } from "./router";

function App() {
  return <RouterProvider router={router} />;
}

export default App;
