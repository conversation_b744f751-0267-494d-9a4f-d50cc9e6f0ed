import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch, increment } from "../store";

export default function Home() {
  const count = useSelector((state: RootState) => state.counter.value);
  const dispatch = useDispatch<AppDispatch>();

  return (
    <div>
      <h1>Home Page</h1>
      <p>Count from Redux: {count}</p>
      <button onClick={() => dispatch(increment())}>Increment</button>
    </div>
  );
}
