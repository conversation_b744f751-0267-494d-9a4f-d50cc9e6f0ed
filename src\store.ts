import { configureStore, createSlice } from "@reduxjs/toolkit";
import { loadState, saveState } from "./localStorage";

const counterSlice = createSlice({
  name: "counter",
  initialState: { value: 0 },
  reducers: {
    increment: (state) => {
      state.value += 1;
    },
  },
});

export const { increment } = counterSlice.actions;

const persistedState = loadState();

export const store = configureStore({
  reducer: {
    counter: counterSlice.reducer,
  },
  preloadedState: persistedState,
});

store.subscribe(() => {
  saveState({
    counter: store.getState().counter,
  });
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
