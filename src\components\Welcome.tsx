type PropsType = {
  name: string;
  copyright?: number;
};

function Welcome(props: PropsType) {
  return (
    <div>
      Welcome <b>{props.name}</b> to Vite + React!
      <p>&copy; {props.copyright}</p>
    </div>
  );
}

// function Welcome(props: { name: string }) {
//   return (
//     <div>
//       Welcome <b>{props.name}</b> to Vite + React!
//     </div>
//   );
// }

// Destructuring props
// function Welcome({ name, copyright }: { name: string,copyright?: number }) {
//   return (
//     <div>
//       Welcome <b>{name}</b> to Vite + React!
//     </div>
//   );
// }

export default Welcome;
